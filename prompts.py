"""
This module contains prompts used for interacting with the LLM and browser agent.
"""

import json
import os
from functools import lru_cache

# Load category structure from JSON file
@lru_cache(maxsize=1)
def load_category_structure():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, 'category_cleaned.json')
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.dumps(json.load(f), ensure_ascii=False, indent=2)

# Get the category structure
CATEGORY_STRUCTURE = load_category_structure()

def format_query_parser_prompt(query):
    """Format the query parser prompt with both query and category structure."""
    return QUERY_PARSER_PROMPT.format(
        query=query,
        category_structure=CATEGORY_STRUCTURE
    )

# Prompt optimized for Gemini-2.0-Flash-001
QUERY_PARSER_PROMPT = '''
System: You are a strict JSON generator that must follow these rules:
1. Output ONLY a single, valid JSON object
2. Use DOUBLE QUOTES for ALL strings - NEVER use single quotes
3. Every string MUST start AND end with double quotes
4. Escape any double quotes within strings with backslash
5. No trailing commas
6. No comments
7. No extra text before or after the JSON
8. No line breaks within string values
9. All values must be in English or numbers

Input Query: "{query}"

Category Structure:
{category_structure}

Exact Required JSON Format:
{{
    "keyword": "string_value",
    "min_price": number,
    "max_price": number,
    "record_count": number,
    "seller_type": "string_value"
}}

String Rules:
- ALL string values must be in double quotes
- keyword MUST be in Russian language
- keyword must be a single word
- Escape any double quotes in strings with backslash
- No unescaped quotes or special characters
- seller_type must be one of: "All", "Cross-border sellers", "Domestic seller"

Number Rules:
- No quotes around numbers
- Use 0 for unspecified min_price
- Use 999999 for unspecified max_price
- Use 2000 for unspecified record_count
- Numbers must be positive

Example Valid Input: "帮我选品，家电/秤 ...等1项, Ozon 售价(₽):0~19999,Ozon 卖家类型：全部"
Example Valid Output:
{{
    "keyword": "камера",
    "min_price": 0,
    "max_price": 19999,
    "record_count": 2000,
    "seller_type": "All"
}}

Translation Rules:
1. Convert Chinese terms to Russian
2. Combine relevant terms into a single keyword string in Russian
3. Ensure the keyword is descriptive and searchable in Russian

IMPORTANT: Output ONLY the JSON object with no additional text.
'''

# Prompt for browser automation tasks
BROWSER_SEARCH_PROMPT = '''
Use your browser capability to do below tasks for me step by step.

1. Open webpage 'https://seerfar.cn/admin/product-search.html'
2. If you are redirected to login page, use username '{username}', password '{password}'. Input the captcha. If you have already logged in, skip this step.
3. In 'Seller type' choose '{seller_type}' in the dropdown list.
4. In 'Price' value, input Min price with '{min_price}' and Max price with '{max_price}'.
5. In 'Categories' search box:
   - Click the search box to activate it
   - Hover over '{category1}' in the dropdown to see if category2 options appear
   - If category2 is not 'All':
     - Hover over '{category2}' in the dropdown to see if category3 options appear
     - If category3 is not 'All':
       - Hover over '{category3}' in the dropdown
   - Click on the last visible category (either category1, category2, or category3 depending on which ones are not 'All')
   - Verify that the selected category path appears in the search box above, if the category is selected the letter will turn into white with purple background
   - If the letter is not in purple background, or if there isn't any letter in the search box, go to step 5 and do it again.
6. Select 'Search' Button.
7. See if there are any results. If there are no results, check the Category and Price value again.
8. Click 'Export' button and select CSV for the file format. 
9. Change the default value of 200 to 2000 for the export record count.
10. Make sure the file is downloaded successfully with this task, not the previous task.
11. If you failed to download the file, double-confirm the category and price value and try to export again.
'''

KEYWORD_SEARCH_PROMPT = '''
Use your browser capability to do below tasks for me step by step.

1. If you are on a login page, use username '{username}', password '{password}'. Input the captcha. After login, open webpage 'https://seerfar.com/admin/product-search.html' If you have already logged in, skip this step.
2. If there's a popup window for saving the password, click save to close it.
3. In 'Seller type' choose '{seller_type}' in the dropdown list.
4. Make sure the mode is 'Advanced' on the right side. In the 'Advanced' mode, the 'Advanved' letter will turn into white with purple background.
5. In the 'Keyword' search box, enter the following query: '{keyword}', make sure the keyword is in Russian language ONLY and ONLY 1 word is allowed in the search box. Make sure the keyword is shown in the search box.
6. Make sure the keyword is ONLY ONE word
7. Select 'Search' Button.
8. See if there are any results. If there are no results, try to modify the keyword and try to search again.
9. Click 'Export' button and select CSV for the file format.
10. Change the default value of 200 to 500 for the export record count.
11. Make sure the file is downloaded successfully. If not, change the search keyword and try again.
'''

MIX_CATEGORY_PROMPT = '''
Use your browser capability to perform the following tasks step by step.

1. Open the webpage 'https://seerfar.cn/admin/product-search.html'
2. If you are redirected to the login page, use the username '{username}' and the password '{password}'. Input the captcha. If you have already logged in, skip this step.
3. In the dropdown list, select '{seller_type}' as the 'Seller type'.
4. In the 'Price' field, input the Min price as '{min_price}' and the Max price as '{max_price}'.
5. For each category combination in the following lists:
   Category 1 items: {category_1_items}
   Category 3 items: {category_3_items}
   
   Do the following:
   a. In the 'Categories' search box:
      - Click the search box to activate it, so the dropdown list appears.
      - For each category in the Category 1 items:
        - Type or input the category name in the search box.
        - Wait for the dropdown list to appear with matching items.
        - You MUST click the exact matching item in the dropdown list to select it.
        - After clicking, verify that the category is selected (the checkbox is checked or the category path appears in the search box).
      - Repeat for all Category 1 items.

   b. In the 'Categories' search box:
      - Click the search box to activate it.
      - For each category in the Category 3 items:
        - Type or input the category name in the search box.
        - Wait for the dropdown list to appear with 3-tiers of categories.
        - You MUST click the exact 3rd tier category in the dropdown list to select it.
        - After clicking, verify that the category is selected (the category path appears in the search box or the checkbox is checked).
      - Repeat for all Category 3 items.
      - If there are multiple items in the search box, they will be displayed as '+1', '+2', etc. The name of the new category will not be shown.

   c. Select the 'Search' Button
   d. If there are results:
      - Click the 'Export' button and select 'CSV' as the file format
      - Change the default value of 200 to 2000 for the export record count
      - Make sure the file is downloaded successfully
   e. If there are no results, try the next category combination
6. Ensure that at least one file is downloaded successfully with this task.
7. If you failed to download any files, try to modify the category combinations and price value and try again.
'''

# Prompt for summarizing user queries
SESSION_SUMMARY_PROMPT = '''
Summarize the following product search query into a single, clear and concise sentence.
The query is: "{query}"

Return ONLY the summary sentence, without any additional text or formatting.
The summary should capture ONLY the product categories, the output must not longer than 10 words.
Use the language of the query.
'''

COMPARE_PRODUCT_TITLE = '''
You are an expert product matcher. Compare the following two product titles and determine if they refer to the same category. 

Title 1: "{title1}"
Title 2: "{title2}"

Note: The two titles may be in different languages. Use translation or cross-lingual understanding if needed to make your judgment.

Return ONLY a single word: True if they refer to the same category, or False if they do not. Do not return any explanation or extra text.
'''

BACKUP_BROWSER_SEARCH_PROMPT_0419 = '''
Use your browser capability to do below tasks for me step by step.

1. Open webpage 'https://seerfar.cn/admin/product-search.html'
2. If you are redirected to login page, use username '{username}', password '{password}'. Input the captcha. If you have already logged in, skip this step.
3. In 'Seller type' choose '{seller_type}' in the dropdown list.
4. In 'Price' value, input Min price with '{min_price}' and Max price with '{max_price}'.
5. In 'Categories' search box, search for the category '{last_valid_category}'.
6. In the dropdown list, find the category '{category1}/{category2}/{category3}' and click it in the dropdown list. 
7. Select 'Search' Button.
8. See if there are any results. If there are no results, check the Category and Price value again.
9. Click 'Export' button and select CSV for the file format.
10. Change the default value of 200 to 2000 for the export record count.
11. Make sure the file is downloaded successfully with this task, not the previous task.
12. If you failed to download the file, double-confirm the category and price value and try to export again.
'''