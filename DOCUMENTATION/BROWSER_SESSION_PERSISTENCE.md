# Browser Session Persistence for E2B Sandbox

This document explains how to use the browser session persistence functionality in the E2B Browser Use module.

## Overview

Browser session persistence allows you to maintain browser state (cookies, local storage, session data, preferences) across different E2B sandbox instances. This is particularly useful for:

- Maintaining login sessions across automation runs
- Preserving user preferences and settings
- Avoiding repeated authentication steps
- Continuing workflows from where they left off

## How It Works

The system automatically:

1. **On Sandbox Startup**: Restores browser session data from `/home/<USER>/cookie` to `~/.config/browseruse/profiles/default` in the sandbox
2. **During Execution**: Browser automation tasks use the restored session data
3. **On Sandbox Cleanup**: Downloads current browser session data from the sandbox back to `/home/<USER>/cookie` for the next run

## Directory Structure

### Local Storage (Server)
```
/home/<USER>/cookie/
├── Preferences              # Browser preferences and settings
├── Cookies                  # HTTP cookies
├── Local Storage/           # Local storage data
│   └── leveldb/            # LevelDB files for local storage
├── Session Storage/         # Session storage data
├── IndexedDB/              # IndexedDB data
└── ...                     # Other browser profile files
```

### Sandbox Storage
```
~/.config/browseruse/profiles/default/
├── Preferences              # Browser preferences and settings
├── Cookies                  # HTTP cookies
├── Local Storage/           # Local storage data
├── Session Storage/         # Session storage data
└── ...                     # Other browser profile files
```

## Usage Examples

### Automatic Session Persistence (Default)

```python
import e2b_browser_use

# Browser session is automatically restored and saved
result = e2b_browser_use.main(query="Login to website and perform tasks")
```

### Disable Automatic Session Restoration

```python
import e2b_browser_use

# Start with fresh browser session
result = e2b_browser_use.main(
    query="Perform tasks with clean session",
    restore_browser_session=False
)
```

### Manual Session Management

```python
import e2b_browser_use

tasks = [
    # Manually restore session at specific point
    {'type': 'restore_browser_session', 'local_session_dir': '/home/<USER>/cookie'},
    
    # Perform browser automation
    {'type': 'bedrock', 'query': 'Navigate to dashboard'},
    
    # Manually save session at specific point
    {'type': 'save_browser_session', 'local_session_dir': '/home/<USER>/cookie'},
    
    # Continue with more tasks
    {'type': 'bedrock', 'query': 'Download reports'}
]

result = e2b_browser_use.main(tasks=tasks)
```

### Custom Session Directory

```python
import e2b_browser_use

tasks = [
    # Use custom directory for session storage
    {'type': 'restore_browser_session', 'local_session_dir': '/home/<USER>/custom_session'},
    {'type': 'bedrock', 'query': 'Perform automation'},
    {'type': 'save_browser_session', 'local_session_dir': '/home/<USER>/custom_session'}
]

result = e2b_browser_use.main(tasks=tasks)
```

## API Reference

### Functions

#### `download_browser_session_data(desktop, local_session_dir="/home/<USER>/cookie")`

Downloads browser session data from sandbox to local directory.

**Parameters:**
- `desktop`: E2B desktop sandbox instance
- `local_session_dir`: Local directory to save session data (default: "/home/<USER>/cookie")

**Returns:**
- `bool`: True if successful, False otherwise

#### `upload_browser_session_data(desktop, local_session_dir="/home/<USER>/cookie")`

Uploads browser session data from local directory to sandbox.

**Parameters:**
- `desktop`: E2B desktop sandbox instance
- `local_session_dir`: Local directory containing session data (default: "/home/<USER>/cookie")

**Returns:**
- `bool`: True if successful, False otherwise

### Task Types

#### `save_browser_session`

Manually save browser session data during task execution.

```python
{'type': 'save_browser_session', 'local_session_dir': '/path/to/session/dir'}
```

#### `restore_browser_session`

Manually restore browser session data during task execution.

```python
{'type': 'restore_browser_session', 'local_session_dir': '/path/to/session/dir'}
```

## File Handling

The system handles both text and binary files:

- **Text files**: Read and written as UTF-8 text
- **Binary files**: Automatically detected and handled using base64 encoding for transfer

## Error Handling

The system includes comprehensive error handling:

- Missing directories are created automatically
- File read/write errors are logged but don't stop execution
- Failed transfers are reported with detailed error messages
- Partial failures allow successful files to be processed

## Best Practices

1. **Regular Backups**: The `/home/<USER>/cookie` directory contains important session data - consider backing it up
2. **Clean Sessions**: Occasionally start with `restore_browser_session=False` to ensure clean state
3. **Custom Directories**: Use different session directories for different automation workflows
4. **Monitor Logs**: Check logs for session persistence success/failure messages

## Troubleshooting

### Session Not Restored
- Check if `/home/<USER>/cookie` directory exists and contains files
- Verify file permissions on the session directory
- Look for error messages in logs with `[BROWSER_SESSION]` prefix

### Session Not Saved
- Ensure browser automation tasks actually created session data
- Check sandbox logs for file system errors
- Verify the browser profile directory exists in sandbox

### Performance Issues
- Large session directories may slow down transfer
- Consider cleaning old session data periodically
- Monitor disk space usage

## Security Considerations

- Session data may contain sensitive information (cookies, tokens)
- Ensure proper file permissions on session directories
- Consider encrypting session data for sensitive applications
- Regularly rotate session data for security

## Logging

All browser session operations are logged with the `[BROWSER_SESSION]` prefix:

```
[BROWSER_SESSION] Starting download of browser session data to /home/<USER>/cookie
[BROWSER_SESSION] Found 45 files to download
[BROWSER_SESSION] Successfully downloaded: 45 files
[BROWSER_SESSION] Browser session download completed
```

Monitor these logs to track session persistence operations and troubleshoot issues.
